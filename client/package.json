{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "axios": "^1.8.4", "concurrently": "^9.1.2", "ethers": "^5.7.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "styled-components": "^6.1.17", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"assert": "^2.1.0", "autoprefixer": "^10.4.21", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "customize-cra": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "postcss": "^8.5.5", "react-app-rewired": "^2.2.1", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.17", "url": "^0.11.4", "util": "^0.12.5"}}